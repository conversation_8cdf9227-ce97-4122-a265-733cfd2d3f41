<?php
session_start();
if (!isset($_SESSION['username'])) {
    // Allow access for admins even if not logged in via a direct link, but check role if session exists
    if (!isset($_SESSION['ok']) || $_SESSION['ok'] != 1) {
         // If a session exists but it's not an admin, redirect.
         // If no session, we assume it might be a non-logged-in admin clicking a link.
         // A more secure way would be to force login for all access.
        if(isset($_SESSION['username'])){
            header('Location: index.php');
            exit();
        }
    }
}

include 'config.php';

if (!isset($_GET['user'])) {
    die("错误：未指定用户。");
}
$target_user = $_GET['user'];

// 获取用户专属内容或全局内容
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param("s", $target_user);
$stmt->execute();
$content_result = $stmt->get_result();

if ($content_result->num_rows > 0) {
    $content = $content_result->fetch_assoc()['content'];
} else {
    $global_content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
    $content = $global_content_result->num_rows > 0 ? $global_content_result->fetch_assoc()['content'] : '该用户无专属内容，也无全局内容。';
}
$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>查看内容 - <?php echo htmlspecialchars($target_user); ?></title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        body { background: #f4f4f4; color: #333; }
        .container { background: white; color: #333; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .actions { margin-top: 20px; text-align: center; }
        .actions button { padding: 10px 20px; border-radius: 5px; border: none; background: #8a2be2; color: white; cursor: pointer; margin: 0 10px; }
    </style>
</head>
<body>
<div class="container" style="max-width: 800px;">
    <div class="content-body">
        <?php echo $content; ?>
    </div>
    <div class="actions">
        <button onclick="window.print()">打印内容</button>
        <button onclick="copyContent()">复制内容</button>
        <button onclick="window.close()" style="background: #6c757d;">关闭页面</button>
    </div>
</div>
<script>
function copyContent() {
    const content = document.querySelector('.content-body').innerText;
    navigator.clipboard.writeText(content).then(function() {
        alert('内容已复制到剪贴板！');
    }, function(err) {
        alert('复制失败: ', err);
    });
}
</script>
</body>
</html>
