<?php
session_start();
if (!isset($_SESSION['username'])) {
    // Allow access for admins even if not logged in via a direct link, but check role if session exists
    if (!isset($_SESSION['ok']) || $_SESSION['ok'] != 1) {
         // If a session exists but it's not an admin, redirect.
         // If no session, we assume it might be a non-logged-in admin clicking a link.
         // A more secure way would be to force login for all access.
        if(isset($_SESSION['username'])){
            header('Location: index.php');
            exit();
        }
    }
}

include 'config.php';

if (!isset($_GET['user'])) {
    die("错误：未指定用户。");
}
$target_user = $_GET['user'];

// 获取用户专属内容或全局内容
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param("s", $target_user);
$stmt->execute();
$content_result = $stmt->get_result();

if ($content_result->num_rows > 0) {
    $content = $content_result->fetch_assoc()['content'];
} else {
    $global_content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
    $content = $global_content_result->num_rows > 0 ? $global_content_result->fetch_assoc()['content'] : '该用户无专属内容，也无全局内容。';
}
$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>查看内容 - <?php echo htmlspecialchars($target_user); ?></title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: var(--dark-gray);
            align-items: flex-start;
            padding-top: 32px;
        }
        .container {
            background: var(--white);
            color: var(--dark-gray);
            box-shadow: var(--shadow-heavy);
            border: 1px solid rgba(139, 0, 0, 0.1);
            border-radius: var(--border-radius-large);
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-red), var(--accent-red), var(--secondary-red));
        }
        .content-header {
            padding: 24px 24px 0;
            border-bottom: 2px solid var(--light-gray);
            margin-bottom: 24px;
        }
        .content-header h1 {
            color: var(--primary-red);
            font-size: 24px;
            margin: 0 0 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .user-badge {
            background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
            color: var(--white);
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .actions button {
            padding: 12px 24px;
            border-radius: var(--border-radius);
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 8px;
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
            color: var(--white);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--dark-red), var(--primary-red));
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        .btn-secondary {
            background: var(--medium-gray);
            color: var(--white);
        }
        .btn-secondary:hover {
            background: var(--dark-gray);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        .btn-success {
            background: var(--success);
            color: var(--white);
        }
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
    </style>
</head>
<body>
<div class="container" style="max-width: 900px;">
    <div class="content-header">
        <h1>📄 用户内容查看</h1>
        <div class="user-badge"><?php echo htmlspecialchars($target_user); ?></div>
    </div>
    <div class="content-body">
        <?php echo $content; ?>
    </div>
    <div class="actions">
        <button onclick="window.print()" class="btn-primary">🖨️ 打印内容</button>
        <button onclick="copyContent()" class="btn-success">📋 复制内容</button>
        <button onclick="window.close()" class="btn-secondary">❌ 关闭页面</button>
    </div>
</div>
<script>
function copyContent() {
    const content = document.querySelector('.content-body').innerText;
    navigator.clipboard.writeText(content).then(function() {
        alert('内容已复制到剪贴板！');
    }, function(err) {
        alert('复制失败: ', err);
    });
}
</script>
</body>
</html>
