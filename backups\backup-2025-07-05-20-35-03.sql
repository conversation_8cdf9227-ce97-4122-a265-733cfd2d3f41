-- PHP-MySQL-Backup --
-- Time: 2025-07-05 20:35:03
-- Database: `membership_db`
-- ------------------------------

DROP TABLE IF EXISTS `content`;
CREATE TABLE `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `content` text,
  `addtime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO `content` VALUES("1","","<p>
\n	999</p>
\n","2025-07-05 20:32:36");


DROP TABLE IF EXISTS `tab`;
CREATE TABLE `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ok` int(11) NOT NULL DEFAULT '2',
  `addtime` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

INSERT INTO `tab` VALUES("1","admin","$2y$10$3FJRpNqbcUFilaaqp3HbB.JGwf42sFga7jf1XpkRq6RAV5jtK1dLq","1","2025-07-05 20:29:01");
INSERT INTO `tab` VALUES("2","111","$2y$10$PDgy/iczvMQI90MLiVjSDuBBGK3EhogYAQ3dOZVVoOT3EnTr7gJ0a","2","2025-07-05 20:32:53");
INSERT INTO `tab` VALUES("3","222","$2y$10$xUKUlCn9JWZnT3wQUrPU4OkM8E.4zLa.gkqGJy619D5eKp8RBhbSC","1","2025-07-05 20:33:04");


