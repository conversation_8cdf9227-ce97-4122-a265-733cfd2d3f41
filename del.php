<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

if (!isset($_GET['id'])) {
    header('Location: user.php');
    exit();
}

$user_id = $_GET['id'];

// First, get the username to delete their content
$stmt_user = $conn->prepare("SELECT username FROM tab WHERE id = ?");
$stmt_user->bind_param('i', $user_id);
$stmt_user->execute();
$result_user = $stmt_user->get_result();
if($row = $result_user->fetch_assoc()){
    $username_to_delete = $row['username'];
    
    // Delete user-specific content
    $stmt_content = $conn->prepare("DELETE FROM content WHERE user = ?");
    $stmt_content->bind_param('s', $username_to_delete);
    $stmt_content->execute();
    $stmt_content->close();
}
$stmt_user->close();


// Delete the user from tab table
$stmt = $conn->prepare("DELETE FROM tab WHERE id = ?");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$stmt->close();

$conn->close();

header('Location: user.php');
exit();
?>
