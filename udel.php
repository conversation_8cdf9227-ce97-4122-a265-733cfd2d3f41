<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

if (!isset($_GET['user'])) {
    header('Location: user.php');
    exit();
}

$target_user = $_GET['user'];

$stmt = $conn->prepare("DELETE FROM content WHERE user = ?");
$stmt->bind_param('s', $target_user);

if ($stmt->execute()) {
    // Optionally, provide feedback to the user.
    // For simplicity, we just redirect.
} else {
    // Handle error
}

$stmt->close();
$conn->close();

header('Location: user.php');
exit();
?>
