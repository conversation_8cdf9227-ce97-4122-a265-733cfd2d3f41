@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 深红色主题色彩变量 */
    --primary-red: #8B0000;
    --secondary-red: #A52A2A;
    --light-red: #CD5C5C;
    --dark-red: #660000;
    --accent-red: #DC143C;

    /* 中性色 */
    --white: #FFFFFF;
    --light-gray: #F8F9FA;
    --medium-gray: #6C757D;
    --dark-gray: #343A40;
    --black: #000000;

    /* 功能色 */
    --success: #28A745;
    --warning: #FFC107;
    --error: #DC3545;
    --info: #17A2B8;

    /* 阴影和边框 */
    --shadow-light: 0 2px 4px rgba(139, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(139, 0, 0, 0.15);
    --shadow-heavy: 0 8px 24px rgba(139, 0, 0, 0.2);
    --border-radius: 8px;
    --border-radius-large: 12px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--dark-gray);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    position: relative;
}

/* 背景装饰 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 0, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 0, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    background: var(--white);
    padding: 48px;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-heavy);
    width: 100%;
    max-width: 480px;
    border: 1px solid rgba(139, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* 容器装饰边框 */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-red), var(--accent-red), var(--secondary-red));
}

h1, h2 {
    text-align: center;
    color: var(--primary-red);
    margin-bottom: 32px;
    font-weight: 600;
    font-size: 28px;
    letter-spacing: -0.5px;
}

h2 {
    font-size: 24px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 24px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-gray);
    font-size: 14px;
    letter-spacing: 0.25px;
}

input[type="text"],
input[type="password"],
select {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--dark-gray);
    font-size: 16px;
    font-family: inherit;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="password"]:focus,
select:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 3px rgba(139, 0, 0, 0.1);
    transform: translateY(-1px);
}

select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.btn {
    width: 100%;
    padding: 16px 24px;
    border: none;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    color: var(--white);
    font-size: 16px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover {
    background: linear-gradient(135deg, var(--dark-red), var(--primary-red));
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: translateY(0);
}

.error {
    background: linear-gradient(135deg, var(--error), #e74c3c);
    color: var(--white);
    padding: 16px 20px;
    border-radius: var(--border-radius);
    text-align: center;
    margin-bottom: 24px;
    font-weight: 500;
    box-shadow: var(--shadow-light);
    border-left: 4px solid #c0392b;
}

.success {
    background: linear-gradient(135deg, var(--success), #27ae60);
    color: var(--white);
    padding: 16px 20px;
    border-radius: var(--border-radius);
    text-align: center;
    margin-bottom: 24px;
    font-weight: 500;
    box-shadow: var(--shadow-light);
    border-left: 4px solid #229954;
}

p {
    line-height: 1.7;
    text-align: center;
    color: var(--medium-gray);
    margin-bottom: 16px;
}

a {
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: var(--primary-red);
    transition: width 0.3s ease;
}

a:hover {
    color: var(--dark-red);
}

a:hover::after {
    width: 100%;
}

/* 管理面板和控制台样式 */
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: var(--white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-heavy);
    min-height: calc(100vh - 48px);
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 2px solid var(--light-gray);
}

.admin-title {
    color: var(--primary-red);
    font-size: 32px;
    font-weight: 700;
    margin: 0;
}

.admin-nav {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.nav-btn {
    padding: 12px 20px;
    background: var(--white);
    color: var(--primary-red);
    border: 2px solid var(--primary-red);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.nav-btn:hover {
    background: var(--primary-red);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.nav-btn::after {
    display: none;
}

/* 数据表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 24px;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.data-table th {
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    color: var(--white);
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.data-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--light-gray);
    color: var(--dark-gray);
}

.data-table tr:hover {
    background: rgba(139, 0, 0, 0.02);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* 操作按钮样式 */
.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin: 0 4px;
}

.btn-edit {
    background: var(--info);
    color: var(--white);
}

.btn-edit:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-delete {
    background: var(--error);
    color: var(--white);
}

.btn-delete:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-view {
    background: var(--success);
    color: var(--white);
}

.btn-view:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* 状态标签 */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-admin {
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    color: var(--white);
}

.status-user {
    background: linear-gradient(135deg, var(--medium-gray), #5a6268);
    color: var(--white);
}

/* 内容显示区域 */
.content-body {
    background: var(--light-gray);
    padding: 24px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-red);
    margin: 24px 0;
    line-height: 1.8;
    color: var(--dark-gray);
}

.content-body h1, .content-body h2, .content-body h3 {
    color: var(--primary-red);
    margin-top: 24px;
    margin-bottom: 16px;
}

.content-body h1:first-child,
.content-body h2:first-child,
.content-body h3:first-child {
    margin-top: 0;
}

/* 操作区域 */
.actions {
    margin-top: 32px;
    text-align: center;
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.actions button {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actions .btn-primary {
    background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    color: var(--white);
}

.actions .btn-primary:hover {
    background: linear-gradient(135deg, var(--dark-red), var(--primary-red));
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.actions .btn-secondary {
    background: var(--medium-gray);
    color: var(--white);
}

.actions .btn-secondary:hover {
    background: var(--dark-gray);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 16px;
        align-items: flex-start;
        padding-top: 32px;
    }

    .container {
        padding: 32px 24px;
        max-width: 100%;
        margin: 0;
    }

    .admin-container {
        padding: 16px;
        margin: 16px;
        max-width: calc(100% - 32px);
    }

    .admin-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .admin-nav {
        width: 100%;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }

    .data-table {
        font-size: 14px;
    }

    .data-table th,
    .data-table td {
        padding: 12px 8px;
    }

    .actions {
        flex-direction: column;
        gap: 12px;
    }

    .actions button {
        width: 100%;
    }

    h1 {
        font-size: 24px;
    }

    .admin-title {
        font-size: 28px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 24px 16px;
    }

    .admin-container {
        padding: 12px;
        margin: 8px;
        max-width: calc(100% - 16px);
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
        font-size: 12px;
    }

    .nav-btn {
        padding: 10px 12px;
        font-size: 14px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(139, 0, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-red);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: var(--dark-gray);
    color: var(--white);
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    font-weight: 400;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--dark-gray) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 卡片样式 */
.card {
    background: var(--white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(139, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 2px solid var(--light-gray);
    padding-bottom: 16px;
    margin-bottom: 20px;
}

.card-title {
    color: var(--primary-red);
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.card-subtitle {
    color: var(--medium-gray);
    font-size: 14px;
    margin-top: 4px;
}

/* 统计数字样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: linear-gradient(135deg, var(--white), var(--light-gray));
    padding: 24px;
    border-radius: var(--border-radius-large);
    text-align: center;
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--primary-red);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-red);
    display: block;
    margin-bottom: 8px;
}

.stat-label {
    color: var(--medium-gray);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-red);
}

/* 选择框样式优化 */
select option {
    background: var(--white);
    color: var(--dark-gray);
    padding: 8px;
}
