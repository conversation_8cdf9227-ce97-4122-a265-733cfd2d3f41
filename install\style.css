@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap');

body {
    font-family: 'Noto Sans SC', sans-serif;
    background: linear-gradient(135deg, #2c0a2c, #5a1a5a);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
}

.container {
    background: rgba(0, 0, 0, 0.5);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    width: 100%;
    max-width: 500px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

h1, h2 {
    text-align: center;
    color: #f0a5f0;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #dcdcdc;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #8a2be2;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 16px;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #f0a5f0;
    box-shadow: 0 0 15px rgba(240, 165, 240, 0.5);
}

.btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    background: #8a2be2;
    color: #fff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background: #9932cc;
    box-shadow: 0 0 20px rgba(153, 50, 204, 0.7);
}

.error {
    background: #ff4d4d;
    color: #fff;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
}

p {
    line-height: 1.6;
    text-align: center;
}

a {
    color: #f0a5f0;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
