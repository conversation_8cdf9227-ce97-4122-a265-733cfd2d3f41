<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$username = $_SESSION['username'];

// 获取用户信息
$stmt = $conn->prepare("SELECT addtime, ok FROM tab WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$user_result = $stmt->get_result()->fetch_assoc();
$stmt->close();

// 获取用户专属内容或全局内容
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param("s", $username);
$stmt->execute();
$content_result = $stmt->get_result();

if ($content_result->num_rows > 0) {
    $content = $content_result->fetch_assoc()['content'];
} else {
    // 如果没有专属内容，获取最新的全局内容
    $global_content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
    $content = $global_content_result->num_rows > 0 ? $global_content_result->fetch_assoc()['content'] : '暂无内容可显示。';
}
$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户中心</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        body {
            align-items: flex-start;
            padding-top: 32px;
        }
        .user-panel {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 32px;
            margin-top: 24px;
        }
        .user-info {
            background: var(--white);
            padding: 32px 24px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(139, 0, 0, 0.1);
            text-align: center;
            height: fit-content;
            position: relative;
            overflow: hidden;
        }
        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-red), var(--accent-red), var(--secondary-red));
        }
        .content-display {
            background: var(--white);
            padding: 32px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(139, 0, 0, 0.1);
        }
        .user-info h3 {
            color: var(--primary-red);
            font-size: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        .user-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: var(--white);
            font-weight: 700;
        }
        .badge {
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 16px 0;
            display: inline-block;
        }
        .badge-admin {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #8B4513;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }
        .badge-member {
            background: linear-gradient(135deg, var(--success), #27ae60);
            color: var(--white);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        .user-details {
            background: var(--light-gray);
            padding: 16px;
            border-radius: var(--border-radius);
            margin: 20px 0;
            text-align: left;
        }
        .user-details p {
            margin: 8px 0;
            color: var(--dark-gray);
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-details strong {
            color: var(--primary-red);
        }
        .logout-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: var(--error);
            color: var(--white);
            border: none;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        .logout-btn::after {
            display: none;
        }
        .content-display h2 {
            color: var(--primary-red);
            margin-bottom: 24px;
            font-size: 22px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .content-body {
            background: var(--light-gray);
            padding: 24px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-red);
            margin-top: 16px;
            min-height: 300px;
            line-height: 1.8;
            color: var(--dark-gray);
        }
        .welcome-message {
            background: linear-gradient(135deg, rgba(139, 0, 0, 0.05), rgba(165, 42, 42, 0.05));
            padding: 20px;
            border-radius: var(--border-radius);
            border: 1px solid rgba(139, 0, 0, 0.1);
            margin-bottom: 24px;
            text-align: center;
        }
        .welcome-message h2 {
            color: var(--primary-red);
            margin-bottom: 12px;
            font-size: 20px;
        }
        .welcome-message p {
            color: var(--medium-gray);
            margin: 0;
        }
        @media (max-width: 768px) {
            .user-panel {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .user-info {
                order: 2;
            }
            .content-display {
                order: 1;
            }
        }
    </style>
</head>
<body>
<div class="admin-container">
    <div class="welcome-message">
        <h2>🎉 欢迎回来！</h2>
        <p>您已成功登录到会员管理系统</p>
    </div>

    <div class="user-panel">
        <div class="user-info">
            <div class="user-avatar">
                <?php echo strtoupper(substr($username, 0, 1)); ?>
            </div>
            <h3><?php echo htmlspecialchars($username); ?></h3>

            <?php if ($user_result['ok'] == 1): ?>
                <span class="badge badge-admin">👑 VIP 管理员</span>
            <?php else: ?>
                <span class="badge badge-member">⭐ 尊贵会员</span>
            <?php endif; ?>

            <div class="user-details">
                <p><strong>用户ID:</strong> <span><?php echo htmlspecialchars($username); ?></span></p>
                <p><strong>权限等级:</strong> <span><?php echo $user_result['ok'] == 1 ? '管理员' : '普通用户'; ?></span></p>
                <p><strong>加入时间:</strong> <span><?php echo date('Y年m月d日', strtotime($user_result['addtime'])); ?></span></p>
                <p><strong>在线状态:</strong> <span style="color: var(--success);">● 在线</span></p>
            </div>

            <a href="close.php" class="logout-btn">🚪 安全退出</a>
        </div>

        <div class="content-display">
            <h2>📢 系统通知 / 专属内容</h2>
            <div class="content-body">
                <?php echo $content; ?>
            </div>
        </div>
    </div>
</div>
</body>
</html>
