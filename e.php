<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$username = $_SESSION['username'];

// 获取用户信息
$stmt = $conn->prepare("SELECT addtime, ok FROM tab WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$user_result = $stmt->get_result()->fetch_assoc();
$stmt->close();

// 获取用户专属内容或全局内容
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param("s", $username);
$stmt->execute();
$content_result = $stmt->get_result();

if ($content_result->num_rows > 0) {
    $content = $content_result->fetch_assoc()['content'];
} else {
    // 如果没有专属内容，获取最新的全局内容
    $global_content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
    $content = $global_content_result->num_rows > 0 ? $global_content_result->fetch_assoc()['content'] : '暂无内容可显示。';
}
$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户中心</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        .user-panel { display: flex; gap: 20px; }
        .user-info, .content-display { background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px; }
        .user-info { flex: 1; text-align: center; }
        .content-display { flex: 3; }
        .user-info h3 { color: #f0a5f0; }
        .badge { padding: 5px 15px; border-radius: 15px; font-weight: bold; }
        .badge-vip { background: gold; color: #333; }
        .badge-member { background: #4caf50; color: white; }
    </style>
</head>
<body>
<div class="container" style="max-width: 1000px;">
    <h1>用户中心</h1>
    <div class="user-panel">
        <div class="user-info">
            <h3><?php echo htmlspecialchars($username); ?></h3>
            <p>
                <?php if ($user_result['ok'] == 1): ?>
                    <span class="badge badge-vip">VIP 管理员</span>
                <?php else: ?>
                    <span class="badge badge-member">尊贵会员</span>
                <?php endif; ?>
            </p>
            <p>加入时间: <?php echo date('Y-m-d', strtotime($user_result['addtime'])); ?></p>
            <hr>
            <a href="close.php" style="color: #ff7b7b; text-decoration: none;">安全退出</a>
        </div>
        <div class="content-display">
            <h2>系统通知 / 专属内容</h2>
            <div class="content-body" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-top: 10px; min-height: 200px;">
                <?php echo $content; ?>
            </div>
        </div>
    </div>
</div>
</body>
</html>
