<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => '无权访问']);
    exit();
}

include 'config.php';

$backup_dir = 'backups';
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

$file_name = "backup-" . date("Y-m-d-H-i-s") . ".sql";
$file_path = "$backup_dir/$file_name";

try {
    $mysqli = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $mysqli->set_charset('utf8mb4');

    $handle = fopen($file_path, 'w+');
    fwrite($handle, "-- PHP-MySQL-Backup --\n");
    fwrite($handle, "-- Time: " . date('Y-m-d H:i:s') . "\n");
    fwrite($handle, "-- Database: `{$db_name}`\n");
    fwrite($handle, "-- ------------------------------\n\n");

    $tables = [];
    $result = $mysqli->query("SHOW TABLES");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }

    foreach ($tables as $table) {
        $result = $mysqli->query("SELECT * FROM `$table`");
        $num_fields = $result->field_count;

        fwrite($handle, "DROP TABLE IF EXISTS `$table`;\n");
        $row2 = $mysqli->query("SHOW CREATE TABLE `$table`")->fetch_row();
        fwrite($handle, $row2[1] . ";\n\n");

        for ($i = 0; $i < $num_fields; $i++) {
            while ($row = $result->fetch_row()) {
                fwrite($handle, "INSERT INTO `$table` VALUES(");
                for ($j = 0; $j < $num_fields; $j++) {
                    $row[$j] = addslashes($row[$j]);
                    $row[$j] = preg_replace("/\n/", "\\n", $row[$j]);
                    if (isset($row[$j])) {
                        fwrite($handle, '"' . $row[$j] . '"');
                    } else {
                        fwrite($handle, '""');
                    }
                    if ($j < ($num_fields - 1)) {
                        fwrite($handle, ',');
                    }
                }
                fwrite($handle, ");\n");
            }
        }
        fwrite($handle, "\n\n");
    }

    fclose($handle);
    echo json_encode(['success' => true, 'file' => $file_name]);

} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
