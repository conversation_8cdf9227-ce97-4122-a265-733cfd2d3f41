<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? '';

$sql = "SELECT id, username, addtime, ok FROM tab WHERE username LIKE ?";
$params = ["%$search%"];
$types = "s";

if ($filter) {
    $sql .= " AND ok = ?";
    $params[] = $filter;
    $types .= "i";
}

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>会员管理</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        .user-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .user-table th, .user-table td { padding: 12px; border: 1px solid #5a1a5a; text-align: left; }
        .user-table th { background-color: #4a148c; }
        .user-table tr:nth-child(even) { background-color: rgba(255,255,255,0.05); }
        .actions a { color: #f0a5f0; margin-right: 10px; text-decoration: none; }
        .actions a:hover { text-decoration: underline; }
        .filter-form { display: flex; gap: 10px; margin-bottom: 20px; }
        .filter-form input, .filter-form select, .filter-form button { padding: 10px; border-radius: 5px; border: 1px solid #8a2be2; background: rgba(255,255,255,0.1); color: white; }
        .filter-form button { background: #8a2be2; cursor: pointer; }
    </style>
</head>
<body>
<div class="container" style="max-width: 1000px;">
    <h1>会员管理</h1>
    <p><a href="c.php">返回控制台</a></p>
    <form action="user.php" method="get" class="filter-form">
        <input type="text" name="search" placeholder="搜索用户名..." value="<?php echo htmlspecialchars($search); ?>">
        <select name="filter">
            <option value="">所有用户</option>
            <option value="1" <?php if ($filter == '1') echo 'selected'; ?>>管理员</option>
            <option value="2" <?php if ($filter == '2') echo 'selected'; ?>>普通用户</option>
        </select>
        <button type="submit">筛选</button>
    </form>
    <table class="user-table">
        <thead>
            <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>注册时间</th>
                <th>权限</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($row = $result->fetch_assoc()): ?>
            <tr>
                <td><?php echo $row['id']; ?></td>
                <td><?php echo htmlspecialchars($row['username']); ?></td>
                <td><?php echo $row['addtime']; ?></td>
                <td><?php echo $row['ok'] == 1 ? '管理员' : '普通用户'; ?></td>
                <td class="actions">
                    <a href="u.php?user=<?php echo urlencode($row['username']); ?>">指定内容</a>
                    <a href="check.php?user=<?php echo urlencode($row['username']); ?>" target="_blank">查看内容</a>
                    <a href="udel.php?user=<?php echo urlencode($row['username']); ?>" onclick="return confirm('确定要清空该用户的所有内容吗？')">清空内容</a>
                    <a href="del.php?id=<?php echo $row['id']; ?>" onclick="return confirm('确定要删除该用户吗？')">删除</a>
                </td>
            </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>
</body>
</html>
