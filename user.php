<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$search = $_GET['search'] ?? '';
$filter = $_GET['filter'] ?? '';

$sql = "SELECT id, username, addtime, ok FROM tab WHERE username LIKE ?";
$params = ["%$search%"];
$types = "s";

if ($filter) {
    $sql .= " AND ok = ?";
    $params[] = $filter;
    $types .= "i";
}

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>会员管理</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        body {
            align-items: flex-start;
            padding-top: 32px;
        }
        .filter-section {
            background: var(--white);
            padding: 24px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            margin-bottom: 24px;
            border: 1px solid rgba(139, 0, 0, 0.1);
        }
        .filter-form {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 16px;
            align-items: end;
        }
        .filter-form .form-group {
            margin-bottom: 0;
        }
        .filter-form label {
            font-size: 14px;
            font-weight: 500;
            color: var(--dark-gray);
            margin-bottom: 8px;
        }
        .table-container {
            background: var(--white);
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            overflow: hidden;
            border: 1px solid rgba(139, 0, 0, 0.1);
        }
        .table-header {
            padding: 24px;
            border-bottom: 2px solid var(--light-gray);
        }
        .table-header h2 {
            margin: 0;
            color: var(--primary-red);
            font-size: 20px;
        }
        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            padding: 12px 20px;
            background: var(--white);
            color: var(--primary-red);
            border: 2px solid var(--primary-red);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .back-link:hover {
            background: var(--primary-red);
            color: var(--white);
        }
        .back-link::after {
            display: none;
        }
        @media (max-width: 768px) {
            .filter-form {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 14px;
            }
            .actions {
                flex-direction: column;
                gap: 4px;
            }
            .action-btn {
                font-size: 12px;
                padding: 6px 12px;
            }
        }
    </style>
</head>
<body>
<div class="admin-container">
    <div class="admin-header">
        <h1 class="admin-title">👥 会员管理</h1>
        <a href="c.php" class="back-link">← 返回控制台</a>
    </div>

    <div class="filter-section">
        <form action="user.php" method="get" class="filter-form">
            <div class="form-group">
                <label for="search">搜索用户名</label>
                <input type="text" id="search" name="search" placeholder="输入用户名进行搜索..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="form-group">
                <label for="filter">用户类型</label>
                <select id="filter" name="filter">
                    <option value="">所有用户</option>
                    <option value="1" <?php if ($filter == '1') echo 'selected'; ?>>管理员</option>
                    <option value="2" <?php if ($filter == '2') echo 'selected'; ?>>普通用户</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn">🔍 筛选</button>
            </div>
            <div class="form-group">
                <a href="user.php" class="btn btn-secondary" style="text-decoration: none; display: inline-block; text-align: center;">🔄 重置</a>
            </div>
        </form>
    </div>

    <div class="table-container">
        <div class="table-header">
            <h2>用户列表</h2>
        </div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>注册时间</th>
                    <th>权限</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo $row['id']; ?></td>
                    <td><strong><?php echo htmlspecialchars($row['username']); ?></strong></td>
                    <td><?php echo date('Y-m-d H:i', strtotime($row['addtime'])); ?></td>
                    <td>
                        <span class="status-badge <?php echo $row['ok'] == 1 ? 'status-admin' : 'status-user'; ?>">
                            <?php echo $row['ok'] == 1 ? '管理员' : '普通用户'; ?>
                        </span>
                    </td>
                    <td class="actions">
                        <a href="u.php?user=<?php echo urlencode($row['username']); ?>" class="action-btn btn-edit">📝 指定内容</a>
                        <a href="check.php?user=<?php echo urlencode($row['username']); ?>" target="_blank" class="action-btn btn-view">👁️ 查看内容</a>
                        <a href="udel.php?user=<?php echo urlencode($row['username']); ?>" class="action-btn btn-delete" onclick="return confirm('确定要清空该用户的所有内容吗？')">🗑️ 清空内容</a>
                        <a href="del.php?id=<?php echo $row['id']; ?>" class="action-btn btn-delete" onclick="return confirm('确定要删除该用户吗？')">❌ 删除用户</a>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
