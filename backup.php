<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}

$backup_dir = 'backups';
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

function get_backup_files() {
    global $backup_dir;
    $files = array_diff(scandir($backup_dir), array('.', '..'));
    $file_details = [];
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $file_details[] = [
                'name' => $file,
                'size' => filesize("$backup_dir/$file"),
                'time' => filemtime("$backup_dir/$file")
            ];
        }
    }
    return $file_details;
}

if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['file'])) {
    $file_to_delete = basename($_GET['file']);
    if (file_exists("$backup_dir/$file_to_delete")) {
        unlink("$backup_dir/$file_to_delete");
    }
    header('Location: backup.php');
    exit();
}

if (isset($_POST['action']) && $_POST['action'] == 'delete_selected') {
    if (!empty($_POST['selected_files'])) {
        foreach ($_POST['selected_files'] as $file) {
            $file_to_delete = basename($file);
            if (file_exists("$backup_dir/$file_to_delete")) {
                unlink("$backup_dir/$file_to_delete");
            }
        }
    }
    header('Location: backup.php');
    exit();
}

$backup_files = get_backup_files();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据备份</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        .backup-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .backup-table th, .backup-table td { padding: 12px; border: 1px solid #5a1a5a; text-align: left; }
        .backup-table th { background-color: #4a148c; }
        .backup-table tr:nth-child(even) { background-color: rgba(255,255,255,0.05); }
        .actions a { color: #f0a5f0; margin-right: 10px; text-decoration: none; }
        .progress-bar { width: 100%; background-color: #ddd; border-radius: 5px; display: none; }
        .progress-bar-inner { width: 0; height: 20px; background-color: #4caf50; border-radius: 5px; text-align: center; color: white; line-height: 20px; }
    </style>
</head>
<body>
<div class="container" style="max-width: 1000px;">
    <h1>数据备份管理</h1>
    <p><a href="c.php">返回控制台</a></p>
    <button class="btn" onclick="startBackup()">创建新的备份</button>
    <div id="progress-container" style="margin-top: 15px;">
        <div class="progress-bar">
            <div class="progress-bar-inner" id="progress-bar-inner">0%</div>
        </div>
        <p id="backup-status"></p>
    </div>
    <hr>
    <h2>现有备份文件</h2>
    <form action="backup.php" method="post" id="backup-form">
        <input type="hidden" name="action" value="delete_selected">
        <table class="backup-table">
            <thead>
                <tr>
                    <th><input type="checkbox" onclick="toggle(this);"></th>
                    <th>文件名</th>
                    <th>文件大小</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($backup_files)): ?>
                    <tr><td colspan="5" style="text-align: center;">没有备份文件。</td></tr>
                <?php else: ?>
                    <?php foreach ($backup_files as $file): ?>
                    <tr>
                        <td><input type="checkbox" name="selected_files[]" value="<?php echo htmlspecialchars($file['name']); ?>"></td>
                        <td><?php echo htmlspecialchars($file['name']); ?></td>
                        <td><?php echo round($file['size'] / 1024, 2); ?> KB</td>
                        <td><?php echo date('Y-m-d H:i:s', $file['time']); ?></td>
                        <td class="actions">
                            <a href="<?php echo "$backup_dir/" . htmlspecialchars($file['name']); ?>" download>下载</a>
                            <a href="backup.php?action=delete&file=<?php echo urlencode($file['name']); ?>" onclick="return confirm('确定要删除这个备份文件吗？')">删除</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <button type="submit" class="btn" style="background: #d9534f; margin-top: 15px;" onclick="return confirm('确定要删除选中的文件吗？')">删除选中</button>
    </form>
</div>
<script>
function toggle(source) {
    checkboxes = document.getElementsByName('selected_files[]');
    for(var i=0, n=checkboxes.length;i<n;i++) {
        checkboxes[i].checked = source.checked;
    }
}

function startBackup() {
    document.querySelector('.progress-bar').style.display = 'block';
    document.getElementById('backup-status').innerText = '正在准备备份...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'create_backup.php', true);
    
    var progressBar = document.getElementById('progress-bar-inner');
    var status = document.getElementById('backup-status');

    xhr.onprogress = function (e) {
        if (e.lengthComputable) {
            var percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressBar.innerText = Math.round(percentComplete) + '%';
        }
    };

    xhr.onload = function () {
        if (xhr.status === 200) {
            var response = JSON.parse(xhr.responseText);
            if(response.success){
                status.innerText = '备份成功！文件名：' + response.file;
                progressBar.style.width = '100%';
                progressBar.innerText = '100%';
                setTimeout(function(){ location.reload(); }, 2000);
            } else {
                status.innerText = '备份失败：' + response.message;
            }
        } else {
            status.innerText = '备份过程中发生错误。';
        }
    };
    
    xhr.send();
}
</script>
</body>
</html>
