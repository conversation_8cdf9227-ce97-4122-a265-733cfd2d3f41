<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统 - 安装程序</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>会员管理系统 - 安装向导</h1>
        <?php
        if (file_exists('install.lock')) {
            echo '<p class="error">安装程序已被锁定。如果您需要重新安装，请删除根目录下的 `install.lock` 文件。</p>';
        } else {
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $db_host = $_POST['db_host'];
                $db_user = $_POST['db_user'];
                $db_pass = $_POST['db_pass'];
                $db_name = $_POST['db_name'];

                // 1. 生成配置文件
                $config_content = "<?php
// 数据库连接配置
\$db_host = '{$db_host}';
\$db_user = '{$db_user}';
\$db_pass = '{$db_pass}';
\$db_name = '{$db_name}';

// 连接数据库
\$conn = new mysqli(\$db_host, \$db_user, \$db_pass);

// 检查连接
if (\$conn->connect_error) {
    die('数据库连接失败: ' . \$conn->connect_error);
}

// 创建数据库
\$sql_create_db = \"CREATE DATABASE IF NOT EXISTS `{\$db_name}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci\";
if (\$conn->query(\$sql_create_db) === TRUE) {
    // echo '数据库 ' . \$db_name . ' 创建成功或已存在<br>';
} else {
    die('创建数据库失败: ' . \$conn->error);
}

// 选择数据库
\$conn->select_db(\$db_name);

// 设置字符集
\$conn->set_charset('utf8mb4');

// SQL 创建表结构
\$sql_tab = \"
CREATE TABLE IF NOT EXISTS `tab` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `ok` int(11) NOT NULL DEFAULT 2,
  `addtime` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
\";

\$sql_content = \"
CREATE TABLE IF NOT EXISTS `content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `content` text,
  `addtime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
\";

// 执行SQL
if (\$conn->query(\$sql_tab) === TRUE) {
    // echo '用户表 `tab` 创建成功或已存在<br>';
} else {
    die('创建用户表失败: ' . \$conn->error);
}

if (\$conn->query(\$sql_content) === TRUE) {
    // echo '内容表 `content` 创建成功或已存在<br>';
} else {
    die('创建内容表失败: ' . \$conn->error);
}

// 关闭连接
// \$conn->close(); // This is handled by individual scripts
?>";
                
                // 写入配置文件
                if (file_put_contents('../config.php', $config_content)) {
                    // 创建锁定文件
                    file_put_contents('install.lock', 'installed');
                    echo '<h2>🎉 安装成功！</h2>';
                    echo '<p>您的网站已成功安装。为安全起见，安装程序已被锁定。</p>';
                    echo '<p><b>默认管理员账号：admin</b></p>';
                    echo '<p><b>默认管理员密码：123456</b></p>';
                    echo '<p>请<a href="../index.php">点击这里</a>登录，并立即修改您的默认密码。</p>';
                    
                    // 添加默认管理员
                    // The original connection is already connected to the correct database.
                    if (!$conn->connect_error) {
                        $admin_user = 'admin';
                        $admin_pass = password_hash('123456', PASSWORD_DEFAULT);
                        $addtime = date('Y-m-d H:i:s');
                        $stmt = $conn->prepare("INSERT INTO tab (username, password, ok, addtime) VALUES (?, ?, 1, ?)");
                        $stmt->bind_param('sss', $admin_user, $admin_pass, $addtime);
                        $stmt->execute();
                        $stmt->close();
                        $conn->close();
                    }

                } else {
                    echo '<p class="error">无法写入 `config.php` 文件。请检查目录权限。</p>';
                }
            } else {
        ?>
            <form action="index.php" method="POST">
                <div class="form-group">
                    <label for="db_host">数据库主机:</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                <div class="form-group">
                    <label for="db_name">数据库名称:</label>
                    <input type="text" id="db_name" name="db_name" value="membership_db" required>
                </div>
                <div class="form-group">
                    <label for="db_user">数据库用户名:</label>
                    <input type="text" id="db_user" name="db_user" value="root" required>
                </div>
                <div class="form-group">
                    <label for="db_pass">数据库密码:</label>
                    <input type="password" id="db_pass" name="db_pass" value="111111">
                </div>
                <button type="submit" class="btn">立即安装</button>
            </form>
        <?php
            }
        }
        ?>
    </div>
</body>
</html>
