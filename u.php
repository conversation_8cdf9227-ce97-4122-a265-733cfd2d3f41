<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

if (!isset($_GET['user'])) {
    header('Location: user.php');
    exit();
}
$target_user = $_GET['user'];

// 获取该用户当前的专属内容
$stmt = $conn->prepare("SELECT content FROM content WHERE user = ? ORDER BY id DESC LIMIT 1");
$stmt->bind_param("s", $target_user);
$stmt->execute();
$result = $stmt->get_result();
$current_content = $result->num_rows > 0 ? $result->fetch_assoc()['content'] : '';
$stmt->close();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['editor1'])) {
    $new_content = $_POST['editor1'];
    
    // 删除旧的专属内容
    $delete_stmt = $conn->prepare("DELETE FROM content WHERE user = ?");
    $delete_stmt->bind_param('s', $target_user);
    $delete_stmt->execute();
    $delete_stmt->close();

    // 插入新的专属内容
    $insert_stmt = $conn->prepare("INSERT INTO content (user, content, addtime) VALUES (?, ?, NOW())");
    $insert_stmt->bind_param('ss', $target_user, $new_content);
    $insert_stmt->execute();
    $insert_stmt->close();
    
    header('Location: u.php?user=' . urlencode($target_user) . '&status=success');
    exit();
}
$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>为 <?php echo htmlspecialchars($target_user); ?> 指定内容</title>
    <link rel="stylesheet" href="install/style.css">
    <script src="ckeditor/ckeditor.js"></script>
</head>
<body>
<div class="container" style="max-width: 900px;">
    <h1>为用户 <strong><?php echo htmlspecialchars($target_user); ?></strong> 指定内容</h1>
    <p><a href="user.php">返回会员管理</a></p>
    <?php if(isset($_GET['status']) && $_GET['status'] == 'success'): ?>
        <p style="color: #00ff00; background: rgba(0,255,0,0.1); padding: 10px; border-radius: 5px;">内容已成功更新！</p>
    <?php endif; ?>
    <form action="u.php?user=<?php echo urlencode($target_user); ?>" method="post">
        <textarea name="editor1" id="editor1" rows="10" cols="80"><?php echo htmlspecialchars($current_content); ?></textarea>
        <script>CKEDITOR.replace('editor1');</script>
        <br>
        <button type="submit" class="btn">保存内容</button>
    </form>
</div>
</body>
</html>
