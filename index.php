<?php
session_start();
if (isset($_SESSION['username'])) {
    if ($_SESSION['ok'] == 1) {
        header('Location: c.php');
    } else {
        header('Location: e.php');
    }
    exit();
}

$error_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!file_exists('config.php')) {
        header('Location: install/index.php');
        exit();
    }
    include 'config.php';
    $username = $_POST['username'];
    $password = $_POST['password'];

    $stmt = $conn->prepare("SELECT password, ok FROM tab WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $stmt->bind_result($hashed_password, $ok);
        $stmt->fetch();
        if (password_verify($password, $hashed_password)) {
            $_SESSION['username'] = $username;
            $_SESSION['ok'] = $ok;
            if ($ok == 1) {
                header('Location: c.php');
            } else {
                header('Location: e.php');
            }
            exit();
        } else {
            $error_message = '密码错误，请重试。';
        }
    } else {
        $error_message = '用户不存在，请检查您的用户名。';
    }
    $stmt->close();
    $conn->close();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统 - 登录</title>
    <link rel="stylesheet" href="install/style.css">
</head>
<body>
    <div class="container">
        <h1>会员登录</h1>
        <?php if ($error_message): ?>
            <p class="error"><?php echo $error_message; ?></p>
        <?php endif; ?>
        <form action="index.php" method="POST">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">登录</button>
        </form>
    </div>
</body>
</html>
