<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

// 统计数据
$total_users = $conn->query("SELECT COUNT(*) FROM tab")->fetch_row()[0];
$admin_users = $conn->query("SELECT COUNT(*) FROM tab WHERE ok = 1")->fetch_row()[0];
$normal_users = $conn->query("SELECT COUNT(*) FROM tab WHERE ok = 2")->fetch_row()[0];

// 获取全局内容
$content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
$global_content = $content_result->num_rows > 0 ? $content_result->fetch_assoc()['content'] : '暂无内容';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['editor1'])) {
    $new_content = $_POST['editor1'];
    $stmt = $conn->prepare("INSERT INTO content (content, addtime) VALUES (?, NOW())");
    $stmt->bind_param('s', $new_content);
    $stmt->execute();
    $stmt->close();
    header('Location: c.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>管理控制台</title>
    <link rel="stylesheet" href="install/style.css">
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        .dashboard { display: flex; gap: 20px; }
        .sidebar, .main-content { background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px; }
        .sidebar { flex: 1; }
        .main-content { flex: 3; }
        .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
        .stat-item { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center; }
        .stat-item h3 { margin: 0 0 10px; font-size: 18px; color: #f0a5f0; }
        .stat-item p { margin: 0; font-size: 24px; font-weight: bold; }
        .nav-menu a { display: block; background: #8a2be2; color: white; padding: 12px; margin-bottom: 10px; border-radius: 5px; text-decoration: none; text-align: center; transition: background 0.3s; }
        .nav-menu a:hover { background: #9932cc; }
    </style>
</head>
<body>
<div class="container" style="max-width: 1200px;">
    <h1>管理控制台</h1>
    <div class="dashboard">
        <div class="sidebar">
            <h3>欢迎, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h3>
            <p>当前时间: <?php echo date('Y-m-d H:i:s'); ?></p>
            <hr>
            <div class="stats-grid">
                <div class="stat-item"><h3>总会员</h3><p><?php echo $total_users; ?></p></div>
                <div class="stat-item"><h3>管理员</h3><p><?php echo $admin_users; ?></p></div>
                <div class="stat-item"><h3>普通用户</h3><p><?php echo $normal_users; ?></p></div>
            </div>
            <hr>
            <div class="nav-menu">
                <a href="user.php">会员管理</a>
                <a href="add.php">添加会员</a>
                <a href="backup.php">数据备份</a>
                <a href="restore.php">数据恢复</a>
                <a href="close.php">安全退出</a>
            </div>
        </div>
        <div class="main-content">
            <h2>发布全局内容</h2>
            <form action="c.php" method="post">
                <textarea name="editor1" id="editor1" rows="10" cols="80"></textarea>
                <script>CKEDITOR.replace('editor1');</script>
                <br>
                <button type="submit" class="btn">发布内容</button>
            </form>
            <hr>
            <h2>当前内容预览</h2>
            <div class="content-preview" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-top: 20px;">
                <?php echo $global_content; ?>
            </div>
        </div>
    </div>
</div>
</body>
</html>
