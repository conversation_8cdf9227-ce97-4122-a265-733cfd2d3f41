<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

// 统计数据
$total_users = $conn->query("SELECT COUNT(*) FROM tab")->fetch_row()[0];
$admin_users = $conn->query("SELECT COUNT(*) FROM tab WHERE ok = 1")->fetch_row()[0];
$normal_users = $conn->query("SELECT COUNT(*) FROM tab WHERE ok = 2")->fetch_row()[0];

// 获取全局内容
$content_result = $conn->query("SELECT content FROM content WHERE user IS NULL ORDER BY id DESC LIMIT 1");
$global_content = $content_result->num_rows > 0 ? $content_result->fetch_assoc()['content'] : '暂无内容';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['editor1'])) {
    $new_content = $_POST['editor1'];
    $stmt = $conn->prepare("INSERT INTO content (content, addtime) VALUES (?, NOW())");
    $stmt->bind_param('s', $new_content);
    $stmt->execute();
    $stmt->close();
    header('Location: c.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>管理控制台</title>
    <link rel="stylesheet" href="install/style.css">
    <script src="ckeditor/ckeditor.js"></script>
    <style>
        body {
            align-items: flex-start;
            padding-top: 32px;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 32px;
            margin-top: 24px;
        }
        .sidebar {
            background: var(--white);
            padding: 24px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(139, 0, 0, 0.1);
            height: fit-content;
        }
        .main-content {
            background: var(--white);
            padding: 32px;
            border-radius: var(--border-radius-large);
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(139, 0, 0, 0.1);
        }
        .welcome-section {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--light-gray);
        }
        .welcome-section h3 {
            color: var(--primary-red);
            font-size: 18px;
            margin-bottom: 8px;
        }
        .welcome-section p {
            color: var(--medium-gray);
            font-size: 14px;
            margin: 0;
        }
        .sidebar-stats {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        .sidebar-stat {
            background: linear-gradient(135deg, var(--light-gray), #f1f3f4);
            padding: 16px;
            border-radius: var(--border-radius);
            text-align: center;
            border-left: 4px solid var(--primary-red);
        }
        .sidebar-stat h4 {
            margin: 0 0 8px;
            font-size: 14px;
            color: var(--medium-gray);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .sidebar-stat p {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-red);
        }
        .nav-menu {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .nav-menu a {
            display: flex;
            align-items: center;
            background: var(--white);
            color: var(--primary-red);
            padding: 14px 16px;
            border: 2px solid var(--primary-red);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: left;
        }
        .nav-menu a:hover {
            background: var(--primary-red);
            color: var(--white);
            transform: translateX(4px);
        }
        .nav-menu a::after {
            display: none;
        }
        .content-preview {
            background: var(--light-gray);
            padding: 24px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-red);
            margin-top: 24px;
            line-height: 1.8;
        }
        .form-section {
            margin-bottom: 32px;
        }
        .form-section h2 {
            color: var(--primary-red);
            margin-bottom: 20px;
            font-size: 24px;
        }
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .sidebar {
                order: 2;
            }
            .main-content {
                order: 1;
            }
        }
    </style>
</head>
<body>
<div class="admin-container">
    <div class="admin-header">
        <h1 class="admin-title">管理控制台</h1>
    </div>
    <div class="dashboard">
        <div class="sidebar">
            <div class="welcome-section">
                <h3>欢迎, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h3>
                <p><?php echo date('Y年m月d日 H:i:s'); ?></p>
            </div>
            <div class="sidebar-stats">
                <div class="sidebar-stat">
                    <h4>总会员</h4>
                    <p><?php echo $total_users; ?></p>
                </div>
                <div class="sidebar-stat">
                    <h4>管理员</h4>
                    <p><?php echo $admin_users; ?></p>
                </div>
                <div class="sidebar-stat">
                    <h4>普通用户</h4>
                    <p><?php echo $normal_users; ?></p>
                </div>
            </div>
            <div class="nav-menu">
                <a href="user.php">👥 会员管理</a>
                <a href="add.php">➕ 添加会员</a>
                <a href="backup.php">💾 数据备份</a>
                <a href="restore.php">🔄 数据恢复</a>
                <a href="close.php">🚪 安全退出</a>
            </div>
        </div>
        <div class="main-content">
            <div class="form-section">
                <h2>📝 发布全局内容</h2>
                <form action="c.php" method="post">
                    <div class="form-group">
                        <textarea name="editor1" id="editor1" rows="10" cols="80"></textarea>
                        <script>CKEDITOR.replace('editor1');</script>
                    </div>
                    <button type="submit" class="btn">发布内容</button>
                </form>
            </div>
            <div class="form-section">
                <h2>👁️ 当前内容预览</h2>
                <div class="content-preview">
                    <?php echo $global_content; ?>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
