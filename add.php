<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $ok = $_POST['ok'];

    if ($password !== $confirm_password) {
        $message = '<p class="error">两次输入的密码不匹配。</p>';
    } else {
        $stmt = $conn->prepare("SELECT id FROM tab WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $stmt->store_result();
        if ($stmt->num_rows > 0) {
            $message = '<p class="error">用户名已存在。</p>';
        } else {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $addtime = date('Y-m-d H:i:s');
            $insert_stmt = $conn->prepare("INSERT INTO tab (username, password, ok, addtime) VALUES (?, ?, ?, ?)");
            $insert_stmt->bind_param("ssis", $username, $hashed_password, $ok, $addtime);
            if ($insert_stmt->execute()) {
                $message = '<p style="color: #00ff00; background: rgba(0,255,0,0.1); padding: 10px; border-radius: 5px;">用户添加成功！</p>';
            } else {
                $message = '<p class="error">添加用户失败，请重试。</p>';
            }
            $insert_stmt->close();
        }
        $stmt->close();
    }
    $conn->close();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>添加新会员</title>
    <link rel="stylesheet" href="install/style.css">
</head>
<body>
<div class="container">
    <h1>添加新会员</h1>
    <p><a href="c.php">返回控制台</a></p>
    <?php echo $message; ?>
    <form action="add.php" method="POST">
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <div class="form-group">
            <label for="confirm_password">确认密码:</label>
            <input type="password" id="confirm_password" name="confirm_password" required>
        </div>
        <div class="form-group">
            <label for="ok">用户类型:</label>
            <select name="ok" id="ok" style="width: 100%; padding: 12px; border: 1px solid #8a2be2; border-radius: 8px; background: #2a2a2a; color: #fff; font-size: 16px;">
                <option value="2">普通用户</option>
                <option value="1">管理员</option>
            </select>
        </div>
        <button type="submit" class="btn">添加会员</button>
    </form>
</div>
</body>
</html>
