<?php
session_start();
if (!isset($_SESSION['username']) || $_SESSION['ok'] != 1) {
    header('Location: index.php');
    exit();
}
include 'config.php';

$backup_dir = 'backups';
$message = '';

function get_backup_files() {
    global $backup_dir;
    $files = array_diff(scandir($backup_dir), array('.', '..'));
    $sql_files = [];
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $sql_files[] = $file;
        }
    }
    return $sql_files;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['confirm1']) || !isset($_POST['confirm2'])) {
        $message = '<p class="error">您必须勾选所有确认框才能继续。</p>';
    } else {
        $file_to_restore = '';
        if (isset($_POST['restore_from_list']) && !empty($_POST['backup_file'])) {
            $file_to_restore = "$backup_dir/" . basename($_POST['backup_file']);
        } elseif (isset($_FILES['uploaded_file']) && $_FILES['uploaded_file']['error'] == 0) {
            if (pathinfo($_FILES['uploaded_file']['name'], PATHINFO_EXTENSION) != 'sql') {
                $message = '<p class="error">上传失败：只允许上传 .sql 文件。</p>';
            } else {
                $file_to_restore = $_FILES['uploaded_file']['tmp_name'];
            }
        }

        if ($file_to_restore && file_exists($file_to_restore)) {
            $mysqli = new mysqli($db_host, $db_user, $db_pass, $db_name);
            $mysqli->set_charset('utf8mb4');
            $sql = file_get_contents($file_to_restore);
            if ($mysqli->multi_query($sql)) {
                // Clear out the results from the buffer
                while ($mysqli->next_result()) {
                    if ($res = $mysqli->store_result()) {
                        $res->free();
                    }
                }
                $message = '<p class="success">数据库恢复成功！</p>';
            } else {
                $message = '<p class="error">恢复失败: ' . $mysqli->error . '</p>';
            }
            $mysqli->close();
        } elseif(!$message) {
            $message = '<p class="error">请选择一个备份文件或上传一个新的备份文件。</p>';
        }
    }
}

$backup_files = get_backup_files();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据恢复</title>
    <link rel="stylesheet" href="install/style.css">
    <style>
        .restore-section { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .warning { border: 2px solid #ffcc00; padding: 15px; border-radius: 8px; background: rgba(255, 204, 0, 0.1); color: #ffcc00; }
        .success { color: #00ff00; background: rgba(0,255,0,0.1); padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
<div class="container" style="max-width: 800px;">
    <h1>数据恢复</h1>
    <p><a href="c.php">返回控制台</a></p>
    <div class="warning">
        <h2><strong>⚠️ 警告：这是一个危险操作！</strong></h2>
        <p>恢复数据将完全覆盖当前数据库中的所有内容。此操作不可逆，请在继续前确保您已备份好当前数据。</p>
    </div>
    <?php echo $message; ?>
    <div class="restore-section">
        <h2>从现有备份恢复</h2>
        <form action="restore.php" method="post">
            <select name="backup_file" style="width: 100%; padding: 10px; margin-bottom: 10px;">
                <option value="">-- 选择一个备份文件 --</option>
                <?php foreach ($backup_files as $file): ?>
                    <option value="<?php echo htmlspecialchars($file); ?>"><?php echo htmlspecialchars($file); ?></option>
                <?php endforeach; ?>
            </select>
            <label><input type="checkbox" name="confirm1" required> 我确认要覆盖当前数据。</label><br>
            <label><input type="checkbox" name="confirm2" required> 我知道此操作不可撤销。</label><br><br>
            <button type="submit" name="restore_from_list" class="btn">从列表恢复</button>
        </form>
    </div>
    <div class="restore-section">
        <h2>从文件上传恢复</h2>
        <form action="restore.php" method="post" enctype="multipart/form-data">
            <input type="file" name="uploaded_file" accept=".sql" required style="width: 100%; padding: 10px; margin-bottom: 10px;">
            <label><input type="checkbox" name="confirm1" required> 我确认要覆盖当前数据。</label><br>
            <label><input type="checkbox" name="confirm2" required> 我知道此操作不可撤销。</label><br><br>
            <button type="submit" name="restore_from_upload" class="btn">上传并恢复</button>
        </form>
    </div>
</div>
</body>
</html>
